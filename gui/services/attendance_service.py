from datetime import datetime
from facial_recognition_system.database import save_attendance_to_database
from facial_recognition_system.local_database import get_connection

def check_attendance_exists(class_id, subject_id, date=None):
    if date is None:
        date = datetime.now().strftime("%Y-%m-%d")
    try:
        conn = get_connection()
        cursor = conn.cursor()
        query = "SELECT id FROM presences WHERE date = ? AND class_id = ?"
        params = [date, class_id]
        if subject_id:
            query += " AND subject_id = ?"
            params.append(subject_id)
        cursor.execute(query, params)
        result = cursor.fetchall()
        return len(result) > 0
    except Exception as e:
        print(f"Error checking attendance: {str(e)}")
        return False

def save_attendance(attendance_data, class_id, subject_id, session_date=None):
    try:
        if session_date is None:
            session_date = datetime.now().strftime("%Y-%m-%d")
        if not attendance_data:
            return True
        result = save_attendance_to_database(
            attendance_data,
            class_id=class_id,
            subject_id=subject_id,
            session_date=session_date
        )
        return result
    except Exception:
        return False

def get_attendance_records(class_id=None, subject_id=None, date_from=None, date_to=None, student_id=None):
    try:
        conn = get_connection()
        cursor = conn.cursor()
        query = """
        SELECT a.id, a.student_id, a.class_id, a.status, a.date, a.time, a.created_at,
               s.name as student_name, c.name as class_name
        FROM presences a
        LEFT JOIN etudiants s ON a.student_id = s.id
        LEFT JOIN classes c ON a.class_id = c.id
        WHERE 1=1
        """
        params = []
        if class_id:
            query += " AND a.class_id = ?"
            params.append(class_id)
        # Subject functionality removed - subject_id parameter ignored
        if student_id:
            query += " AND a.student_id = ?"
            params.append(student_id)
        if date_from:
            query += " AND a.date >= ?"
            params.append(date_from)
        if date_to:
            query += " AND a.date <= ?"
            params.append(date_to)
        query += " ORDER BY a.date DESC, a.time DESC"
        cursor.execute(query, params)
        return cursor.fetchall()
    except Exception as e:
        print(f"Error getting attendance records: {str(e)}")
        return []



def get_attendance_records_count(class_id=None, subject_id=None, date_from=None, date_to=None, student_id=None):
    """Get total count of attendance records matching the filters"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        query = """
        SELECT COUNT(*) as total
        FROM presences a
        WHERE 1=1
        """
        params = []
        if class_id:
            query += " AND a.class_id = ?"
            params.append(class_id)
        if subject_id:
            query += " AND a.subject_id = ?"
            params.append(subject_id)
        if student_id:
            query += " AND a.student_id = ?"
            params.append(student_id)
        if date_from:
            query += " AND a.date >= ?"
            params.append(date_from)
        if date_to:
            query += " AND a.date <= ?"
            params.append(date_to)

        cursor.execute(query, params)
        result = cursor.fetchone()
        return result['total'] if result else 0
    except Exception as e:
        print(f"Error getting attendance records count: {str(e)}")
        return 0

def get_recent_attendance_records(limit=100):
    """Get recent attendance records with a default limit for performance"""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        query = """
        SELECT a.id, a.student_id, a.class_id, a.subject_id, a.status, a.date, a.time, a.created_at,
               s.name as student_name, c.name as class_name, sub.name as subject_name
        FROM presences a
        LEFT JOIN etudiants s ON a.student_id = s.id
        LEFT JOIN classes c ON a.class_id = c.id
        LEFT JOIN matieres sub ON a.subject_id = sub.id
        ORDER BY a.date DESC, a.time DESC
        LIMIT ?
        """
        cursor.execute(query, [limit])
        return cursor.fetchall()
    except Exception as e:
        print(f"Error getting recent attendance records: {str(e)}")
        return []

def delete_attendance_record(attendance_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM presences WHERE id = ?", (attendance_id,))
        conn.commit()
        return True
    except Exception as e:
        print(f"Error deleting attendance record: {str(e)}")
        return False
