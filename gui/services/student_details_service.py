"""
Student details service for comprehensive student information retrieval.
"""
from facial_recognition_system.local_database import get_connection
from datetime import datetime, timedelta


def get_student_details(student_id):
    """
    Get comprehensive details for a specific student.

    Args:
        student_id: The student ID

    Returns:
        dict: Student details including basic info, attendance stats, and quiz performance
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Get basic student information
        cursor.execute("""
            SELECT s.id, s.name, s.created_at, s.updated_at, s.code,
                   c.name as class_name, c.id as class_id
            FROM etudiants s
            LEFT JOIN classes c ON s.class_id = c.id
            WHERE s.id = ?
        """, (student_id,))

        student_row = cursor.fetchone()
        if not student_row:
            return None

        student = dict(student_row)

        # Check if face encoding exists
        student['has_face_encoding'] = bool(student.get('code'))

        # Get attendance statistics
        attendance_stats = get_student_attendance_stats(student_id)
        student.update(attendance_stats)

        # Get quiz performance
        quiz_stats = get_student_quiz_stats(student_id)
        student.update(quiz_stats)

        # Get recent activity
        recent_activity = get_student_recent_activity(student_id)
        student['recent_activity'] = recent_activity

        return student

    except Exception as e:
        print(f"Error getting student details: {e}")
        return None


def get_student_attendance_stats(student_id):
    """
    Get attendance statistics for a student.

    Args:
        student_id: The student ID

    Returns:
        dict: Attendance statistics
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Get total attendance records
        cursor.execute("""
            SELECT COUNT(*) as total_sessions,
                   SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as present_count,
                   SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as absent_count
            FROM presences
            WHERE student_id = ?
        """, (student_id,))

        stats_row = cursor.fetchone()
        if stats_row:
            stats = dict(stats_row)
            total = stats['total_sessions']
            present = stats['present_count']

            # Calculate attendance rate
            attendance_rate = (present / total * 100) if total > 0 else 0
            stats['attendance_rate'] = attendance_rate

            # Subject functionality removed - no longer tracking attendance by subject

            # Subject attendance tracking removed
            stats['subject_attendance'] = []
            return stats

        return {
            'total_sessions': 0,
            'present_count': 0,
            'absent_count': 0,
            'attendance_rate': 0,
            'subject_attendance': []
        }

    except Exception as e:
        print(f"Error getting attendance stats: {e}")
        return {
            'total_sessions': 0,
            'present_count': 0,
            'absent_count': 0,
            'attendance_rate': 0,
            'subject_attendance': []
        }


def get_student_quiz_stats(student_id):
    """
    Get quiz performance statistics for a student.

    Args:
        student_id: The student ID

    Returns:
        dict: Quiz performance statistics
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Get student name first
        cursor.execute("SELECT name FROM etudiants WHERE id = ?", (student_id,))
        student_row = cursor.fetchone()
        if not student_row:
            return {'quiz_stats': {}}

        student_name = student_row['name']

        # Check if quiz tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='soumissions_quiz'")
        if cursor.fetchone():
            # Get quiz submissions by student name
            cursor.execute("""
                SELECT COUNT(*) as total_quizzes,
                       AVG(CAST(score AS FLOAT) / CAST(total_questions AS FLOAT) * 100) as average_score,
                       MAX(CAST(score AS FLOAT) / CAST(total_questions AS FLOAT) * 100) as best_score,
                       MIN(CAST(score AS FLOAT) / CAST(total_questions AS FLOAT) * 100) as worst_score
                FROM soumissions_quiz
                WHERE student_name = ?
            """, (student_name,))

            quiz_row = cursor.fetchone()
            if quiz_row and quiz_row['total_quizzes'] > 0:
                quiz_stats = dict(quiz_row)

                # Check if quiz table exists for recent submissions
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='quiz'")
                if cursor.fetchone():
                    # Get recent quiz submissions with submission IDs
                    cursor.execute("""
                        SELECT q.title as quiz_title, s.id as submission_id, s.quiz_id, s.score, s.total_questions, s.submitted_at,
                               CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100 as percentage
                        FROM soumissions_quiz s
                        JOIN quiz q ON s.quiz_id = q.id
                        WHERE s.student_name = ?
                        ORDER BY s.submitted_at DESC
                        LIMIT 10
                    """, (student_name,))

                    recent_quizzes = []
                    for row in cursor.fetchall():
                        recent_quizzes.append(dict(row))

                    quiz_stats['recent_quizzes'] = recent_quizzes
                else:
                    quiz_stats['recent_quizzes'] = []

                return {'quiz_stats': quiz_stats}

        return {'quiz_stats': {
            'total_quizzes': 0,
            'average_score': 0,
            'best_score': 0,
            'worst_score': 0,
            'recent_quizzes': []
        }}

    except Exception as e:
        print(f"Error getting quiz stats: {e}")
        return {'quiz_stats': {
            'total_quizzes': 0,
            'average_score': 0,
            'best_score': 0,
            'worst_score': 0,
            'recent_quizzes': []
        }}


def get_student_recent_activity(student_id, days=30):
    """
    Get recent activity for a student.

    Args:
        student_id: The student ID
        days: Number of days to look back

    Returns:
        list: Recent activity records
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Calculate date threshold
        date_threshold = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        # Get recent attendance records
        cursor.execute("""
            SELECT 'attendance' as activity_type,
                   p.status,
                   p.date,
                   p.time,
                   p.created_at
            FROM presences p
            WHERE p.student_id = ? AND p.date >= ?
            ORDER BY p.created_at DESC
            LIMIT 10
        """, (student_id, date_threshold))

        activities = []
        for row in cursor.fetchall():
            activity = dict(row)
            activity['description'] = f"{'Présent' if activity['status'] else 'Absent'}"
            activities.append(activity)

        # Get student name for quiz activities
        cursor.execute("SELECT name FROM etudiants WHERE id = ?", (student_id,))
        student_row = cursor.fetchone()
        if student_row:
            student_name = student_row['name']

            # Check if quiz tables exist before querying
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='soumissions_quiz'")
            if cursor.fetchone():
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='quiz'")
                if cursor.fetchone():
                    # Get recent quiz submissions
                    cursor.execute("""
                        SELECT 'quiz' as activity_type,
                               q.title as quiz_title,
                               s.score,
                               s.total_questions,
                               s.submitted_at as created_at
                        FROM soumissions_quiz s
                        JOIN quiz q ON s.quiz_id = q.id
                        WHERE s.student_name = ? AND DATE(s.submitted_at) >= ?
                        ORDER BY s.submitted_at DESC
                        LIMIT 5
                    """, (student_name, date_threshold))

                    for row in cursor.fetchall():
                        activity = dict(row)
                        percentage = (activity['score'] / activity['total_questions'] * 100) if activity['total_questions'] > 0 else 0
                        activity['description'] = f"Quiz: {activity['quiz_title']} - {activity['score']}/{activity['total_questions']} ({percentage:.1f}%)"
                        activities.append(activity)

        # Sort all activities by date
        activities.sort(key=lambda x: x['created_at'], reverse=True)

        return activities[:15]  # Return top 15 most recent activities

    except Exception as e:
        print(f"Error getting recent activity: {e}")
        return []


def get_student_attendance_history(student_id, limit=50):
    """
    Get detailed attendance history for a student.

    Args:
        student_id: The student ID
        limit: Maximum number of records to return

    Returns:
        list: Attendance history records
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT p.date, p.time, p.status,
                   m.name as subject_name,
                   c.name as class_name
            FROM presences p
            LEFT JOIN matieres m ON p.subject_id = m.id
            LEFT JOIN classes c ON p.class_id = c.id
            WHERE p.student_id = ?
            ORDER BY p.date DESC, p.time DESC
            LIMIT ?
        """, (student_id, limit))

        records = []
        for row in cursor.fetchall():
            records.append(dict(row))

        return records

    except Exception as e:
        print(f"Error getting attendance history: {e}")
        return []


def get_quiz_submission_details(submission_id):
    """
    Get detailed quiz submission information including all answers.

    Args:
        submission_id: The quiz submission ID

    Returns:
        dict: Detailed submission data with questions and answers
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Get submission basic info
        cursor.execute("""
            SELECT s.*, q.title as quiz_title, q.description as quiz_description
            FROM soumissions_quiz s
            JOIN quiz q ON s.quiz_id = q.id
            WHERE s.id = ?
        """, (submission_id,))

        submission_row = cursor.fetchone()
        if not submission_row:
            return None

        submission = dict(submission_row)

        # Get detailed answers with questions and options
        cursor.execute("""
            SELECT DISTINCT qq.id as question_id, qq.question_text, qq.question_order
            FROM reponses_quiz qa
            JOIN questions_quiz qq ON qa.question_id = qq.id
            WHERE qa.submission_id = ?
            ORDER BY qq.question_order
        """, (submission_id,))

        question_rows = cursor.fetchall()
        detailed_answers = []

        for question_row in question_rows:
            question_id = question_row['question_id']

            # Get all selected options for this question
            cursor.execute("""
                SELECT qa.selected_option_id, qa.is_correct,
                       COALESCE(opt.option_text, 'Aucune réponse') as selected_option_text
                FROM reponses_quiz qa
                LEFT JOIN options_quiz opt ON qa.selected_option_id = opt.id
                WHERE qa.submission_id = ? AND qa.question_id = ?
            """, (submission_id, question_id))

            selected_answers = cursor.fetchall()

            # Get all correct options for this question
            cursor.execute("""
                SELECT option_text
                FROM options_quiz
                WHERE question_id = ? AND is_correct = 1
                ORDER BY option_order
            """, (question_id,))

            correct_options = [row['option_text'] for row in cursor.fetchall()]

            # Get all options for this question
            cursor.execute("""
                SELECT option_text, is_correct, option_order
                FROM options_quiz
                WHERE question_id = ?
                ORDER BY option_order
            """, (question_id,))

            all_options = [dict(row) for row in cursor.fetchall()]

            # Determine if the question was answered correctly
            selected_texts = [ans['selected_option_text'] for ans in selected_answers if ans['selected_option_text'] != 'Aucune réponse']
            question_is_correct = len(selected_answers) > 0 and all(ans['is_correct'] for ans in selected_answers)

            answer_data = {
                'question_id': question_id,
                'question_text': question_row['question_text'],
                'question_order': question_row['question_order'],
                'selected_options': selected_texts,
                'correct_options': correct_options,
                'all_options': all_options,
                'is_correct': question_is_correct,
                'answered': len(selected_texts) > 0
            }
            detailed_answers.append(answer_data)

        submission['detailed_answers'] = detailed_answers
        submission['percentage'] = (submission['score'] / submission['total_questions'] * 100) if submission['total_questions'] > 0 else 0

        return submission

    except Exception as e:
        print(f"Error getting quiz submission details: {e}")
        return None


def get_all_student_quiz_submissions(student_name):
    """
    Get all quiz submissions for a student with basic info.

    Args:
        student_name: The student name

    Returns:
        list: List of all quiz submissions
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Check if quiz tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='soumissions_quiz'")
        if not cursor.fetchone():
            return []

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='quiz'")
        if not cursor.fetchone():
            return []

        cursor.execute("""
            SELECT s.id as submission_id, s.quiz_id, s.score, s.total_questions, s.submitted_at,
                   q.title as quiz_title, q.description as quiz_description,
                   CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100 as percentage
            FROM soumissions_quiz s
            JOIN quiz q ON s.quiz_id = q.id
            WHERE s.student_name = ?
            ORDER BY s.submitted_at DESC
        """, (student_name,))

        submissions = []
        for row in cursor.fetchall():
            submissions.append(dict(row))

        return submissions

    except Exception as e:
        print(f"Error getting student quiz submissions: {e}")
        return []