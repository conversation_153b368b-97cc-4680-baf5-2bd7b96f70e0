"""
Subject management service for the Teacher Assistant application.
"""
from datetime import datetime
from facial_recognition_system.local_database import get_connection


def get_class_subjects(class_id=None):
    """Fetch class subjects from local database

    Args:
        class_id (str, optional): ID of the class to fetch subjects for. If None, fetch all subjects.

    Returns:
        list: List of subject dictionaries
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if class_id:
            cursor.execute("SELECT * FROM matieres WHERE class_id = ? ORDER BY name", (class_id,))
        else:
            cursor.execute("SELECT * FROM matieres ORDER BY name")

        rows = cursor.fetchall()

        if not rows:
            return []

        # Convert rows to list of dictionaries
        subjects = []
        for row in rows:
            subject = dict(row)
            # Rename 'name' to 'subject_name' for compatibility
            subject['subject_name'] = subject.pop('name')
            # Add empty teacher_name for compatibility
            subject['teacher_name'] = ""
            subjects.append(subject)

        return subjects
    except Exception as e:
        return []


def create_class_subject(class_id, subject_name, description="", teacher_name=""):
    """Create a new class subject in the local database

    Args:
        class_id (str): ID of the class
        subject_name (str): Name of the subject (e.g., Math, Computer Science)
        description (str, optional): Description of the subject
        teacher_name (str, optional): Name of the teacher (not used in local database)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if subject already exists for this class
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "SELECT * FROM matieres WHERE class_id = ? AND name = ?",
            (class_id, subject_name)
        )
        existing = cursor.fetchone()

        if existing:
            return False

        # Create new subject
        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO matieres (name, class_id, description, created_at) VALUES (?, ?, ?, ?)",
            (subject_name, class_id, description, now)
        )
        conn.commit()

        return True
    except Exception as e:
        return False


def update_class_subject(subject_id, subject_name=None, description=None, teacher_name=None):
    """Update a class subject in the local database

    Args:
        subject_id (str): ID of the subject to update
        subject_name (str, optional): New name for the subject
        description (str, optional): New description for the subject
        teacher_name (str, optional): New teacher name (not used in local database)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if subject exists
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM matieres WHERE id = ?", (subject_id,))
        existing_subject = cursor.fetchone()

        if not existing_subject:
            return False

        # Update subject
        now = datetime.now().isoformat()

        # Build the SQL query dynamically based on what needs to be updated
        update_fields = ["updated_at = ?"]
        params = [now]

        if subject_name is not None:
            update_fields.append("name = ?")
            params.append(subject_name)

        if description is not None:
            update_fields.append("description = ?")
            params.append(description)

        # Add the subject_id to the parameters
        params.append(subject_id)

        # Construct and execute the SQL query
        sql = f"UPDATE matieres SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(sql, params)
        conn.commit()

        return True
    except Exception as e:
        return False


def delete_class_subject(subject_id):
    """Delete a class subject from the local database

    Args:
        subject_id (str): ID of the subject to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if subject exists
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM matieres WHERE id = ?", (subject_id,))
        existing_subject = cursor.fetchone()

        if not existing_subject:
            return False

        # Delete subject
        cursor.execute("DELETE FROM matieres WHERE id = ?", (subject_id,))
        conn.commit()

        return True
    except Exception as e:
        return False
