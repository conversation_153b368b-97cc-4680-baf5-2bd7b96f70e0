"""
Application state management.
"""
import flet as ft
from gui.config.constants import COLOR_ERROR
from gui.config.language import DEFAULT_LANGUAGE, get_text

class AppState:
    """
    Manages the application state including current selections and UI state.
    """
    def __init__(self, page: ft.Page):
        """Initialize the application state."""
        self.page = page
        self.is_loading = False

        # Current selections
        self.current_class_id = None
        self.current_class_name = None
        self.current_subject_id = None
        self.current_subject_name = None
        self.current_student_id = None
        self.current_student_name = None

        # Language settings
        self.language = DEFAULT_LANGUAGE
        page.language = DEFAULT_LANGUAGE

    def set_current_class(self, class_id: str, class_name: str):
        """Set the current class."""
        self.current_class_id = class_id
        self.current_class_name = class_name

    def set_current_subject(self, subject_id: str, subject_name: str):
        """Set the current subject - DEPRECATED: Subject functionality removed."""
        # Subject functionality removed
        pass

    def set_current_student(self, student_id: str, student_name: str):
        """Set the current student."""
        self.current_student_id = student_id
        self.current_student_name = student_name

    def show_message(self, message, color=None):
        """Show a message."""
        print(f"{'ERROR: ' if color == COLOR_ERROR else ''}{message}")

    def show_error(self, message):
        """Show an error message."""
        print(f"ERROR: {message}")

    def show_success(self, message):
        """Show a success message."""
        print(message)

    def show_snackbar(self, message, is_error=False):
        """Show a snackbar message."""
        print(f"{'ERROR: ' if is_error else ''}{message}")

    def close_dialog(self):
        """Close the current dialog."""
        if self.page.dialog:
            self.page.dialog.open = False
            self.page.update()

    def refresh_current_view(self):
        """Refresh the current view without navigation."""
        route = self.page.route
        # Try to use the direct refresh function if available
        if hasattr(self.page, '_refresh_class_cards') and route == "/classes":
            self.page._refresh_class_cards()
        else:
            # Fallback to route navigation
            # Force a page update before navigation to ensure clean state
            self.page.update()
            # Navigate to the same route to trigger view refresh
            self.page.go(route)

    def show_connection_error_dialog(self, message=None):
        """Show a database error dialog."""
        current_language = getattr(self.page, 'language', DEFAULT_LANGUAGE)
        if message is None:
            message = get_text("database_error_message", current_language)

        self.page.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(get_text("database_error", current_language), color=COLOR_ERROR),
            content=ft.Text(message),
            actions=[
                ft.TextButton(get_text("retry", current_language), on_click=lambda _: self.close_dialog()),
                ft.TextButton(get_text("close", current_language), on_click=lambda _: self.close_dialog())
            ],
        )
        self.page.dialog.open = True
        self.page.update()

    def set_language(self, language_code):
        """Set the application language."""
        self.language = language_code
        self.page.language = language_code
        self.refresh_current_view()
