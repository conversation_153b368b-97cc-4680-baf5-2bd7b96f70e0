"""
Classes view for the application.
"""
import flet as ft
from gui.components.layout import create_page_layout
from gui.components.cards import create_class_card
from gui.components.dialogs import show_dialog, close_dialog
from gui.services.class_service import (
    get_existing_classes, create_class, get_students_in_class,
    assign_student_to_class, remove_student_from_class,
    delete_class, update_class_name
)
from facial_recognition_system.database import get_existing_records
from gui.utils.qr_code import generate_enrollment_url, get_local_ip, start_enrollment_server, stop_enrollment_server
from gui.utils.video_stream import generate_video_stream_url
from gui.state import AppState
from gui.config.constants import ICON_ADD
from gui.config.language import get_text

def create_classes_view(page: ft.Page):
    if getattr(page, 'app_state', None) is None:
        page.app_state = AppState(page)

    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)
    classes = get_existing_classes()

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("class_management", current_language),
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                f"{len(classes)} {get_text('classes_available', current_language)}",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center,
    )

    # Create a modern column for class cards
    class_cards_container = ft.Column(
        spacing=16,
        width=page.width*0.9 if is_mobile else 600,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
    )

    # Store reference to containers for dynamic updates
    page._class_cards_container = class_cards_container

    # Function to handle search
    def on_search_change(_):
        """Handle search field changes"""
        search_query = search_field.value if search_field.value else ""
        refresh_class_cards(search_query)

    # Create search bar
    search_field = ft.TextField(
        hint_text=get_text("search_classes", current_language),
        prefix_icon=ft.Icons.SEARCH,
        border_radius=12,
        content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
        width=page.width*0.9 if is_mobile else 500,
        bgcolor=ft.Colors.SURFACE,
        border_color=ft.Colors.OUTLINE_VARIANT,
        focused_border_color=ft.Colors.PRIMARY,
        on_change=on_search_change,
    )

    # Function to filter classes based on search query
    def filter_classes(all_classes, search_query):
        """Filter classes based on search query"""
        if not search_query:
            return all_classes

        search_query = search_query.lower().strip()
        filtered_classes = {}

        for class_name, class_info in all_classes.items():
            if search_query in class_name.lower():
                filtered_classes[class_name] = class_info

        return filtered_classes

    # Function to refresh class cards dynamically
    def refresh_class_cards(search_query=""):
        """Refresh the class cards container with updated data"""
        # Clear existing cards
        class_cards_container.controls.clear()

        # Get fresh data from database
        fresh_classes = get_existing_classes()

        # Filter classes based on search query
        filtered_classes = filter_classes(fresh_classes, search_query)

        # Update welcome section with new count
        if search_query:
            welcome_section.content.controls[1].value = f"{len(filtered_classes)} {get_text('of_classes_found', current_language).format(total=len(fresh_classes))}"
        else:
            welcome_section.content.controls[1].value = f"{len(fresh_classes)} {get_text('classes_available', current_language)}"

        # Update no classes message visibility and content
        no_classes_message.visible = len(filtered_classes) == 0

        if len(filtered_classes) == 0:
            # Update message content based on whether we're searching
            if search_query:
                # Searching but no results
                no_classes_message.content.controls[0].name = ft.Icons.SEARCH_OFF
                no_classes_message.content.controls[1].value = get_text("no_classes_found", current_language).format(query=search_query)
                no_classes_message.content.controls[2].value = get_text("try_different_search", current_language)
            else:
                # No classes at all
                no_classes_message.content.controls[0].name = ft.Icons.CLASS_
                no_classes_message.content.controls[1].value = get_text("no_classes_yet", current_language)
                no_classes_message.content.controls[2].value = get_text("create_first_class", current_language)
            page.update()
            return

        # Recreate cards with filtered data
        for class_name, class_info in filtered_classes.items():
            # Get students in this class
            students = get_students_in_class(class_info['id'])
            student_count = len(students) if students else 0
            class_id = class_info['id']

            # Create handlers
            def create_view_handler(class_id, class_name):
                def handle_view_click(_):
                    show_view_students_dialog(page, class_id, class_name)
                return handle_view_click

            def create_rename_handler(class_id, class_name):
                def handle_rename_click(_):
                    show_rename_class_dialog(page, class_id, class_name)
                return handle_rename_click

            def create_delete_handler(class_id, class_name):
                def handle_delete_click(_):
                    show_delete_class_dialog(page, class_id, class_name)
                return handle_delete_click

            def create_assign_handler(class_id, class_name):
                def handle_assign_click(_):
                    show_assign_students_dialog(page, class_id, class_name)
                return handle_assign_click

            def create_attendance_handler(class_id, class_name):
                def handle_attendance_click(_):
                    show_take_attendance_dialog(page, class_id, class_name)
                return handle_attendance_click

            def create_qr_code_handler(class_id, class_name):
                def handle_qr_code_click(_):
                    show_qr_code_dialog(page, class_id, class_name)
                return handle_qr_code_click

            # Create card
            class_card = create_class_card(
                class_name,
                student_count,
                class_id,
                create_view_handler(class_id, class_name),
                create_rename_handler(class_id, class_name),
                create_delete_handler(class_id, class_name),
                create_assign_handler(class_id, class_name),
                create_attendance_handler(class_id, class_name),
                create_qr_code_handler(class_id, class_name),
                on_attendance_history=create_attendance_history_handler(class_id, class_name),
                page=page
            )

            # Add card to container
            class_cards_container.controls.append(class_card)

        # Update the page
        page.update()

    # Store the refresh function for use in dialogs
    page._refresh_class_cards = refresh_class_cards

    class_name_field = ft.TextField(
        hint_text=get_text("enter_class_name", current_language),
        border_radius=8,
        expand=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
    )

    class_description_field = ft.TextField(
        hint_text=get_text("enter_class_description", current_language),
        border_radius=8,
        expand=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        multiline=True,
        max_lines=2,
    )

    def add_new_class(_):
        if not class_name_field.value:
            class_name_field.error_text = get_text("please_enter_class_name", current_language)
            page.update()
            return

        success = create_class(class_name_field.value, class_description_field.value or "")
        if success:
            class_name_field.value = ""
            class_description_field.value = ""
            class_name_field.error_text = ""
            # Clear search and refresh
            search_field.value = ""
            refresh_class_cards()
        else:
            page.app_state.show_error(f"{get_text('failed_to_create_class', current_language)} '{class_name_field.value}'")

    add_class_button = ft.ElevatedButton(
        get_text("add", current_language),
        icon=ICON_ADD,
        tooltip=get_text("add_class", current_language),
        on_click=add_new_class,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=12),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            shadow_color=ft.Colors.BLUE_200,
            elevation=2
        ),
    )

    # Create class cards for each existing class (initial load)
    filtered_classes = filter_classes(classes, "")
    for class_name, class_info in filtered_classes.items():
        # Get students in this class
        students = get_students_in_class(class_info['id'])
        student_count = len(students) if students else 0

        # Store class ID and name for button handlers
        class_id = class_info['id']

        # Create view students dialog handler
        def create_view_handler(class_id, class_name):
            def handle_view_click(_):
                show_view_students_dialog(page, class_id, class_name)
            return handle_view_click

        # Create rename class dialog handler
        def create_rename_handler(class_id, class_name):
            def handle_rename_click(_):
                show_rename_class_dialog(page, class_id, class_name)
            return handle_rename_click

        # Create delete class dialog handler
        def create_delete_handler(class_id, class_name):
            def handle_delete_click(_):
                show_delete_class_dialog(page, class_id, class_name)
            return handle_delete_click

        # Create assign students dialog handler
        def create_assign_handler(class_id, class_name):
            def handle_assign_click(_):
                show_assign_students_dialog(page, class_id, class_name)
            return handle_assign_click

        # Create take attendance dialog handler
        def create_attendance_handler(class_id, class_name):
            def handle_attendance_click(_):
                show_take_attendance_dialog(page, class_id, class_name)
            return handle_attendance_click

        # Create QR code enrollment dialog handler
        def create_qr_code_handler(class_id, class_name):
            def handle_qr_code_click(_):
                show_qr_code_dialog(page, class_id, class_name)
            return handle_qr_code_click

        # Create attendance history dialog handler
        def create_attendance_history_handler(class_id, class_name):
            def handle_attendance_history_click(_):
                show_attendance_history_dialog(page, class_id, class_name)
            return handle_attendance_history_click

        # Create a card for this class
        class_card = create_class_card(
            class_name,
            student_count,
            class_id,
            create_view_handler(class_id, class_name),
            create_rename_handler(class_id, class_name),
            create_delete_handler(class_id, class_name),
            create_assign_handler(class_id, class_name),
            create_attendance_handler(class_id, class_name),
            create_qr_code_handler(class_id, class_name),
            on_attendance_history=create_attendance_history_handler(class_id, class_name),
            page=page  # Pass page to enable responsive design
        )

        # Add the card to the container
        class_cards_container.controls.append(class_card)

    # Modern add class form with enhanced styling
    add_class_form = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("add_new_class", current_language),
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Row([class_name_field, add_class_button], spacing=12),
            class_description_field,
        ], spacing=12),
        width=page.width*0.9 if is_mobile else 500,
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        ),
    )

    # Modern no classes message (will be updated dynamically)
    no_classes_message = ft.Container(
        content=ft.Column([
            ft.Icon(
                ft.Icons.CLASS_,
                size=48,
                color=ft.Colors.GREY_400
            ),
            ft.Text(
                get_text("no_classes_yet", current_language),
                size=18,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.W_500
            ),
            ft.Text(
                get_text("create_first_class", current_language),
                size=14,
                color=ft.Colors.GREY_500,
                text_align=ft.TextAlign.CENTER
            )
        ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        alignment=ft.alignment.center,
        padding=ft.padding.all(40),
        visible=len(filtered_classes) == 0,
        bgcolor=ft.Colors.BLUE_GREY_200,
        border_radius=ft.border_radius.all(16),
        margin=ft.margin.only(bottom=20)
    )

    # Create search bar container
    search_container = ft.Container(
        content=search_field,
        alignment=ft.alignment.center,
        margin=ft.margin.only(bottom=20),
    )

    # Modern content layout with welcome section
    content = [
        welcome_section,
        search_container,
        ft.Container(
            content=add_class_form,
            alignment=ft.alignment.center,
        ),
        no_classes_message,
        ft.Container(
            content=class_cards_container,
            alignment=ft.alignment.center,
        )
    ]

    return create_page_layout(
        page,
        "",  # Empty title since we have the welcome section
        content
    )

# Dialog functions for class management
def show_view_students_dialog(page, class_id, class_name):
    """
    Show enhanced dialog with students in a class, including search and detailed information.

    Args:
        page: The Flet page object
        class_id: The class ID
        class_name: The class name
    """
    current_language = getattr(page, 'language', 'fr')
    from gui.services.attendance_service import get_attendance_records
    from facial_recognition_system.local_database import get_connection

    # Get detailed student information
    def get_detailed_students():
        try:
            conn = get_connection()
            cursor = conn.cursor()

            # Get students with their enrollment dates
            cursor.execute("""
                SELECT s.id, s.name, s.created_at, s.updated_at
                FROM etudiants s
                WHERE s.class_id = ?
                ORDER BY s.name
            """, (class_id,))

            students_data = []
            for row in cursor.fetchall():
                student_id = row['id']
                student_name = row['name']
                created_at = row['created_at']
                updated_at = row['updated_at']

                # Get attendance statistics for this student
                attendance_records = get_attendance_records(
                    class_id=class_id,
                    student_id=student_id
                )

                total_sessions = len(attendance_records)
                present_count = sum(1 for record in attendance_records if record['status'])
                attendance_rate = (present_count / total_sessions * 100) if total_sessions > 0 else 0

                students_data.append({
                    'id': student_id,
                    'name': student_name,
                    'created_at': created_at,
                    'updated_at': updated_at,
                    'total_sessions': total_sessions,
                    'present_count': present_count,
                    'attendance_rate': attendance_rate
                })

            return students_data
        except Exception as e:
            print(f"Error getting detailed students: {e}")
            return []

    # Get all students data
    all_students = get_detailed_students()

    # Create search field
    search_field = ft.TextField(
        hint_text=get_text("search_students", current_language),
        prefix_icon=ft.Icons.SEARCH,
        border_radius=8,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        width=400,
    )

    # Create students container
    students_container = ft.Column(
        spacing=8,
        scroll=ft.ScrollMode.AUTO,
        height=400,
        width=500
    )

    # Function to filter and display students
    def filter_and_display_students(search_query=""):
        students_container.controls.clear()

        # Filter students based on search query
        filtered_students = all_students
        if search_query:
            search_query = search_query.lower().strip()
            filtered_students = [
                student for student in all_students
                if search_query in student['name'].lower()
            ]

        if not filtered_students:
            if search_query:
                students_container.controls.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.SEARCH_OFF, size=48, color=ft.Colors.GREY_400),
                            ft.Text(
                                get_text("no_students_found", current_language).format(query=search_query),
                                size=16,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                get_text("try_different_search", current_language),
                                size=14,
                                color=ft.Colors.GREY_500,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        alignment=ft.alignment.center,
                        padding=ft.padding.all(40)
                    )
                )
            else:
                students_container.controls.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.PERSON_OFF, size=48, color=ft.Colors.GREY_400),
                            ft.Text(
                                get_text("no_students_in_class", current_language).format(class_name=class_name),
                                size=16,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                get_text("add_students_to_class", current_language),
                                size=14,
                                color=ft.Colors.GREY_500,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        alignment=ft.alignment.center,
                        padding=ft.padding.all(40)
                    )
                )
        else:
            # Create student cards
            for student in filtered_students:
                # Format dates
                try:
                    from datetime import datetime
                    created_date = datetime.fromisoformat(student['created_at']).strftime("%Y-%m-%d")
                except:
                    created_date = get_text("unknown", current_language)

                # Create attendance status color
                attendance_rate = student['attendance_rate']
                if attendance_rate >= 80:
                    attendance_color = ft.Colors.GREEN
                elif attendance_rate >= 60:
                    attendance_color = ft.Colors.ORANGE
                else:
                    attendance_color = ft.Colors.RED

                # Create click handler for student details
                def create_student_click_handler(student_id):
                    def handle_click(_):
                        close_dialog(page)  # Close the current dialog
                        page.go(f"/student/{student_id}")  # Navigate to student details
                    return handle_click

                student_card = ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            # Header with name and ID
                            ft.Row([
                                ft.Icon(ft.Icons.PERSON, color=ft.Colors.PRIMARY),
                                ft.Column([
                                    ft.Text(
                                        student['name'],
                                        size=16,
                                        weight=ft.FontWeight.BOLD,
                                        color=ft.Colors.ON_SURFACE
                                    ),
                                    ft.Text(
                                        f"ID: {student['id']}",
                                        size=12,
                                        color=ft.Colors.GREY_600
                                    )
                                ], spacing=2, expand=True),
                                ft.Container(
                                    content=ft.Text(
                                        f"{attendance_rate:.1f}%",
                                        size=14,
                                        weight=ft.FontWeight.BOLD,
                                        color=ft.Colors.WHITE
                                    ),
                                    bgcolor=attendance_color,
                                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                    border_radius=12
                                )
                            ], spacing=12),

                            # Details row
                            ft.Row([
                                ft.Column([
                                    ft.Text(get_text("enrolled", current_language), size=10, color=ft.Colors.GREY_600),
                                    ft.Text(created_date, size=12, weight=ft.FontWeight.W_500)
                                ], spacing=2),
                                ft.Container(width=1, height=30, bgcolor=ft.Colors.GREY_300),
                                ft.Column([
                                    ft.Text(get_text("sessions", current_language), size=10, color=ft.Colors.GREY_600),
                                    ft.Text(str(student['total_sessions']), size=12, weight=ft.FontWeight.W_500)
                                ], spacing=2),
                                ft.Container(width=1, height=30, bgcolor=ft.Colors.GREY_300),
                                ft.Column([
                                    ft.Text(get_text("present", current_language), size=10, color=ft.Colors.GREY_600),
                                    ft.Text(str(student['present_count']), size=12, weight=ft.FontWeight.W_500)
                                ], spacing=2),
                            ], spacing=12, alignment=ft.MainAxisAlignment.SPACE_AROUND)
                        ], spacing=12),
                        padding=ft.padding.all(16),
                        on_click=create_student_click_handler(student['id']),
                        ink=True  # Add ripple effect on click
                    ),
                    elevation=2,
                    margin=ft.margin.symmetric(vertical=4)
                )

                students_container.controls.append(student_card)

        page.update()

    # Handle search changes
    def on_search_change(_):
        search_query = search_field.value if search_field.value else ""
        filter_and_display_students(search_query)

    search_field.on_change = on_search_change

    # Initial display
    filter_and_display_students()

    # Create summary header
    total_students = len(all_students)
    if total_students > 0:
        avg_attendance = sum(s['attendance_rate'] for s in all_students) / total_students
        summary_text = f"{total_students} {get_text('students_count', current_language)} • {avg_attendance:.1f}% {get_text('avg_attendance', current_language)}"
    else:
        summary_text = get_text("no_students_in_this_class", current_language)

    summary_header = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("students_in", current_language).format(class_name=class_name),
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Text(
                summary_text,
                size=14,
                color=ft.Colors.GREY_600
            )
        ], spacing=4),
        padding=ft.padding.only(bottom=16)
    )

    # Create dialog content
    content = ft.Column([
        summary_header,
        search_field,
        ft.Container(height=8),  # Spacing
        students_container
    ], spacing=0, width=520)

    # Create dialog
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.PEOPLE, color=ft.Colors.PRIMARY),
            ft.Text(get_text("class_students", current_language), weight=ft.FontWeight.BOLD)
        ], spacing=8),
        content=content,
        actions=[
            ft.TextButton(get_text("close", current_language), on_click=lambda _: close_dialog(page)),
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )

    # Show the dialog
    show_dialog(page, dialog)

def show_rename_class_dialog(page, class_id, class_name):
    """
    Show dialog to rename a class.

    Args:
        page: The Flet page object
        class_id: The class ID
        class_name: The class name
    """
    current_language = getattr(page, 'language', 'fr')
    # Create text field for new name
    new_name_field = ft.TextField(
        label=get_text("new_class_name", current_language),
        value=class_name,
        width=300,
        autofocus=True
    )

    # Function to handle rename
    def do_rename():
        if not new_name_field.value:
            new_name_field.error_text = get_text("please_enter_class_name", current_language)
            page.update()
            return

        if new_name_field.value == class_name:
            close_dialog(page)
            return

        # Rename the class
        success = update_class_name(class_id, new_name_field.value)
        if success:
            # Close the dialog first
            close_dialog(page)

            # Refresh class cards immediately
            page._refresh_class_cards("")
        else:
            page.app_state.show_error(get_text("failed_to_rename_class", current_language))

    # Create form controls
    form_controls = [
        ft.Text(get_text("enter_new_name_for_class", current_language)),
        new_name_field,
    ]

    # Create custom dialog with refresh on close
    def close_and_refresh(_):
        close_dialog(page)

    dialog = ft.AlertDialog(
        title=ft.Text(get_text("rename_class", current_language).format(class_name=class_name)),
        content=ft.Column(
            form_controls,
            scroll=ft.ScrollMode.AUTO,
            height=200,
            width=400,
        ),
        actions=[
            ft.TextButton(get_text("cancel", current_language), on_click=close_and_refresh),
            ft.TextButton(get_text("rename", current_language), on_click=lambda _: do_rename()),
        ],
    )

    show_dialog(page, dialog)

def show_delete_class_dialog(page, class_id, class_name):
    """
    Show dialog to confirm class deletion.

    Args:
        page: The Flet page object
        class_id: The class ID
        class_name: The class name
    """
    current_language = getattr(page, 'language', 'fr')
    # Function to handle deletion
    def do_delete():
        # Delete the class
        success = delete_class(class_id)
        if success:
            # Close the dialog first
            close_dialog(page)

            # Refresh class cards immediately
            page._refresh_class_cards("")
        else:
            page.app_state.show_error(get_text("failed_to_delete_class", current_language))

    # Create custom dialog with refresh on close
    def close_and_refresh(_):
        close_dialog(page)

    dialog = ft.AlertDialog(
        title=ft.Text(get_text("delete_class", current_language).format(class_name=class_name)),
        content=ft.Text(get_text("are_you_sure_delete_class", current_language)),
        actions=[
            ft.TextButton(get_text("cancel", current_language), on_click=close_and_refresh),
            ft.TextButton(
                get_text("delete", current_language),
                on_click=lambda _: do_delete(),
                style=ft.ButtonStyle(color=ft.Colors.RED)
            ),
        ],
    )

    show_dialog(page, dialog)

def show_assign_students_dialog(page, class_id, class_name):
    """
    Show dialog to assign students to a class.

    Args:
        page: The Flet page object
        class_id: The class ID
        class_name: The class name
    """
    # Get all students
    all_students = get_existing_records()

    # Get students already in this class
    class_students = get_students_in_class(class_id)
    class_student_ids = [student_id for _, student_id in class_students]

    # Create checkboxes for each student
    student_checkboxes = []
    for student_name, student_info in all_students.items():
        student_id = student_info['id']
        is_in_class = student_id in class_student_ids

        checkbox = ft.Checkbox(
            label=student_name,
            value=is_in_class,
            data=student_id,  # Store student ID in data attribute
        )
        student_checkboxes.append(checkbox)

    # Function to handle assignment
    def do_assign():
        # Get selected students
        selected_students = [cb for cb in student_checkboxes if cb.value]
        unselected_students = [cb for cb in student_checkboxes if not cb.value and cb.data in class_student_ids]

        # Assign selected students to class
        success_count = 0
        for checkbox in selected_students:
            if checkbox.data not in class_student_ids:
                if assign_student_to_class(checkbox.data, class_id):
                    success_count += 1

        # Remove unselected students from class
        for checkbox in unselected_students:
            if checkbox.data in class_student_ids:
                remove_student_from_class(checkbox.data)

        # Close the dialog first
        close_dialog(page)

        # Refresh class cards immediately
        page._refresh_class_cards("")

    # Create form controls
    current_language = getattr(page, 'language', 'fr')
    form_controls = [ft.Text(get_text("select_students_to_assign", current_language))] + student_checkboxes

    # Create custom dialog with refresh on close
    def close_and_refresh(_):
        close_dialog(page)

    dialog = ft.AlertDialog(
        title=ft.Text(get_text("assign_students_to", current_language).format(class_name=class_name)),
        content=ft.Column(
            form_controls,
            scroll=ft.ScrollMode.AUTO,
            height=400,
            width=400,
        ),
        actions=[
            ft.TextButton(get_text("cancel", current_language), on_click=close_and_refresh),
            ft.TextButton(get_text("save", current_language), on_click=lambda _: do_assign()),
        ],
    )

    show_dialog(page, dialog)

def show_take_attendance_dialog(page, class_id, class_name):
    """
    Show dialog to take attendance for a class.

    Args:
        page: The Flet page object
        class_id: The class ID
        class_name: The class name
    """
    current_language = getattr(page, 'language', 'fr')

    # Function to handle taking attendance
    def go_to_attendance():
        # Store the selected class in app state
        page.app_state.set_current_class(class_id, class_name)

        # Close the dialog
        close_dialog(page)

        # Generate video stream URL without subject
        stream_url = generate_video_stream_url(
            class_id=class_id,
            class_name=class_name
        )

        if stream_url:
            # Open the video stream directly in the browser
            page.launch_url(stream_url)
        else:
            page.app_state.show_error(get_text("failed_to_start_video_stream", current_language))

    # Create simple dialog without subject selection
    dialog = ft.AlertDialog(
        title=ft.Text(get_text("take_attendance_title", current_language).format(class_name=class_name)),
        content=ft.Text(get_text("start_attendance_session", current_language)),
        actions=[
            ft.TextButton(get_text("cancel", current_language), on_click=lambda _: close_dialog(page)),
            ft.TextButton(get_text("start_attendance", current_language), on_click=lambda _: go_to_attendance()),
        ],
    )

    show_dialog(page, dialog)

def show_qr_code_dialog(page, class_id, class_name):
    """
    Show enrollment QR code on the face display and provide a dialog to close enrollment.
    Args:
        page: The Flet page object
        class_id: The class ID
        class_name: The class name
    """
    current_language = getattr(page, 'language', 'fr')
    # Start the enrollment server if it's not already running
    _, enrollment_port = start_enrollment_server()
    # Get local IP address
    local_ip = get_local_ip()
    # Generate enrollment URL with the correct port
    full_url = generate_enrollment_url(class_id, class_name, host=local_ip, port=enrollment_port, shorten=False)
    short_url = generate_enrollment_url(class_id, class_name, host=local_ip, port=enrollment_port, shorten=True, verify_ssl=False)

    # Switch the face display to show enrollment QR code
    from gui.services.qr_display_service import switch_to_enrollment_display
    success = switch_to_enrollment_display(full_url, class_name)

    if not success:
        print(f"❌ Failed to display enrollment QR code for class: {class_name}")
        # Show error message
        snack_bar = ft.SnackBar(
            content=ft.Text(get_text("failed_to_display_enrollment_qr", current_language)),
            action="OK",
            action_color=ft.Colors.ERROR,
            duration=3000
        )
        page.overlay.append(snack_bar)
        snack_bar.open = True
        page.update()
        return

    print(f"✅ {get_text('enrollment_qr_displayed', current_language).format(class_name=class_name)}")

    # Determine if we're on a mobile device
    is_mobile = getattr(page, 'is_mobile', False)

    # Create enrollment control dialog content
    content = ft.Container(
        content=ft.Column([
            # Status indicator
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.CIRCLE, color=ft.Colors.GREEN, size=16),
                    ft.Text(get_text("enrollment_active", current_language), size=16, color=ft.Colors.BLACK, weight=ft.FontWeight.BOLD),
                ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                padding=ft.padding.all(12),
                border_radius=20,
                bgcolor=ft.Colors.GREEN_50,
                margin=ft.margin.only(bottom=20),
            ),
            # Information
            ft.Text(
                get_text("qr_code_for_class_showing", current_language).format(class_name=class_name),
                size=14,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.W_500
            ),
            ft.Text(
                get_text("students_can_scan_enrollment", current_language),
                size=12,
                text_align=ft.TextAlign.CENTER,
                color=ft.Colors.GREY_600
            ),
            # Share link section
            ft.Container(
                content=ft.Column([
                    ft.Text(get_text("or_share_this_link", current_language), size=12, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                    ft.Container(
                        content=ft.Text(
                            short_url,
                            size=11,
                            color=ft.Colors.PRIMARY,
                            text_align=ft.TextAlign.CENTER,
                            selectable=True
                        ),
                        padding=ft.padding.all(8),
                        bgcolor=ft.Colors.SURFACE,
                        border_radius=4,
                    ),
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(12),
                bgcolor=ft.Colors.BLUE_GREY_50,
                border_radius=8,
                margin=ft.margin.only(top=20),
            ),
        ], spacing=10, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=20,
        width=400 if not is_mobile else page.width * 0.9,
    )

    # Function to handle dialog close and stop enrollment server
    def handle_close(_):
        # Stop the enrollment server when dialog is closed
        from gui.services.qr_display_service import switch_back_to_face_from_enrollment

        stop_enrollment_server()
        switch_back_to_face_from_enrollment()

        # Close the dialog
        from gui.components.dialogs import close_dialog
        close_dialog(page)

        # Show confirmation message
        snack_bar = ft.SnackBar(
            content=ft.Text(get_text("enrollment_closed_message", current_language).format(class_name=class_name)),
            action="OK",
            action_color=ft.Colors.PRIMARY,
            duration=4000
        )
        page.overlay.append(snack_bar)
        snack_bar.open = True
        page.update()

    # Create enrollment control dialog
    dialog = ft.AlertDialog(
        title=ft.Text(
            get_text("enrollment_active_title", current_language).format(class_name=class_name),
            weight=ft.FontWeight.BOLD,
            size=18,
        ),
        content=content,
        actions=[
            ft.ElevatedButton(
                get_text("close_enrollment", current_language),
                icon=ft.Icons.STOP,
                on_click=handle_close,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE,
                    bgcolor=ft.Colors.RED_500,
                    shape=ft.RoundedRectangleBorder(radius=8),
                ),
                height=40,
            ),
        ],
        actions_alignment=ft.MainAxisAlignment.END,
        modal=True,
        actions_padding=ft.padding.all(20),
        content_padding=ft.padding.all(20),
    )

    # Add on_dismiss handler to stop enrollment server if dialog is closed
    def on_dismiss(_):
        print(get_text("enrollment_dialog_dismissed", current_language))
        # Stop the enrollment server when dialog is dismissed
        from gui.services.qr_display_service import switch_back_to_face_from_enrollment

        stop_enrollment_server()
        switch_back_to_face_from_enrollment()

        # Show confirmation message
        snack_bar = ft.SnackBar(
            content=ft.Text(get_text("enrollment_closed_message", current_language).format(class_name=class_name)),
            action="OK",
            action_color=ft.Colors.PRIMARY,
            duration=4000
        )
        page.overlay.append(snack_bar)
        snack_bar.open = True
        page.update()

    dialog.on_dismiss = on_dismiss

    # Show the dialog
    from gui.components.dialogs import show_dialog
    show_dialog(page, dialog)

def show_attendance_history_dialog(page, class_id, class_name):
    from gui.views.attendance import create_class_attendance_view

    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Create the attendance view content for this specific class
    attendance_content = create_class_attendance_view(page, class_id, class_name)

    # Enhanced dialog with better design and responsiveness
    dialog = ft.AlertDialog(
        title=ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.HISTORY, color=ft.Colors.PRIMARY, size=24),
                ft.Text(
                    f"{get_text('attendance_history', current_language)}: {class_name}",
                    size=20 if not is_mobile else 18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.ON_SURFACE
                )
            ], spacing=12),
            padding=ft.padding.only(bottom=16)
        ),
        content=ft.Container(
            content=attendance_content,
            width=min(900, page.width * 0.95) if not is_mobile else page.width * 0.95,
            height=min(700, page.height * 0.85) if not is_mobile else page.height * 0.85,
            padding=ft.padding.all(0)
        ),
        actions=[
            ft.Container(
                content=ft.Row([
                    ft.Container(expand=True),
                    ft.ElevatedButton(
                        get_text("close", current_language),
                        on_click=lambda _: close_dialog(page),
                        icon=ft.Icons.CLOSE,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=12),
                            padding=ft.padding.symmetric(horizontal=24, vertical=12)
                        )
                    )
                ], spacing=12),
                padding=ft.padding.only(top=16)
            )
        ],
        actions_padding=ft.padding.all(20),
        content_padding=ft.padding.all(20),
        shape=ft.RoundedRectangleBorder(radius=16),
        bgcolor=ft.Colors.SURFACE,
        surface_tint_color=ft.Colors.SURFACE_TINT
    )

    show_dialog(page, dialog)