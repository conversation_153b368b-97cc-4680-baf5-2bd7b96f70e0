from datetime import datetime
from facial_recognition_system.local_database import get_connection


def initialize_quiz_tables():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS quiz (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        class_id INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS questions_quiz (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quiz_id INTEGER NOT NULL,
        question_text TEXT NOT NULL,
        question_order INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (quiz_id) REFERENCES quiz(id) ON DELETE CASCADE
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS options_quiz (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question_id INTEGER NOT NULL,
        option_text TEXT NOT NULL,
        is_correct BOOLEAN NOT NULL,
        option_order INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (question_id) REFERENCES questions_quiz(id) ON DELETE CASCADE
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS soumissions_quiz (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quiz_id INTEGER NOT NULL,
        student_name TEXT NOT NULL,
        score INTEGER NOT NULL,
        total_questions INTEGER NOT NULL,
        submitted_at TEXT NOT NULL,
        FOREIGN KEY (quiz_id) REFERENCES quiz(id) ON DELETE CASCADE
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS reponses_quiz (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        submission_id INTEGER NOT NULL,
        question_id INTEGER NOT NULL,
        selected_option_id INTEGER,
        is_correct BOOLEAN NOT NULL,
        FOREIGN KEY (submission_id) REFERENCES soumissions_quiz(id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES questions_quiz(id) ON DELETE CASCADE,
        FOREIGN KEY (selected_option_id) REFERENCES options_quiz(id) ON DELETE SET NULL
    )
    ''')

    conn.commit()


def create_quiz(title, description="", class_id=None, subject_id=None):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        # Subject functionality removed - subject_id parameter ignored
        cursor.execute(
            "INSERT INTO quiz (title, description, class_id, created_at) VALUES (?, ?, ?, ?)",
            (title, description, class_id, now)
        )
        conn.commit()

        quiz_id = cursor.lastrowid
        return quiz_id
    except Exception as e:
        return None


def add_question_to_quiz(quiz_id, question_text, question_order=None):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if question_order is None:
            cursor.execute(
                "SELECT MAX(question_order) FROM questions_quiz WHERE quiz_id = ?",
                (quiz_id,)
            )
            result = cursor.fetchone()
            max_order = result[0] if result[0] is not None else 0
            question_order = max_order + 1

        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO questions_quiz (quiz_id, question_text, question_order, created_at) VALUES (?, ?, ?, ?)",
            (quiz_id, question_text, question_order, now)
        )
        conn.commit()

        question_id = cursor.lastrowid
        return question_id
    except Exception as e:
        return None


def add_option_to_question(question_id, option_text, is_correct, option_order=None):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if option_order is None:
            cursor.execute(
                "SELECT MAX(option_order) FROM options_quiz WHERE question_id = ?",
                (question_id,)
            )
            result = cursor.fetchone()
            max_order = result[0] if result[0] is not None else 0
            option_order = max_order + 1

        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO options_quiz (question_id, option_text, is_correct, option_order, created_at) VALUES (?, ?, ?, ?, ?)",
            (question_id, option_text, is_correct, option_order, now)
        )
        conn.commit()

        option_id = cursor.lastrowid
        return option_id
    except Exception as e:
        return None


def get_quizzes(class_id=None, subject_id=None):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        query = "SELECT * FROM quiz"
        params = []

        # Subject functionality removed - subject_id parameter ignored
        if class_id is not None:
            query += " WHERE class_id = ?"
            params.append(class_id)

        query += " ORDER BY created_at DESC"

        cursor.execute(query, params)
        rows = cursor.fetchall()

        quizzes = [dict(row) for row in rows]
        return quizzes
    except Exception as e:
        return []


def get_quiz_with_questions(quiz_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM quiz WHERE id = ?", (quiz_id,))
        quiz_row = cursor.fetchone()

        if not quiz_row:
            return None

        quiz = dict(quiz_row)

        cursor.execute(
            "SELECT * FROM questions_quiz WHERE quiz_id = ? ORDER BY question_order",
            (quiz_id,)
        )
        question_rows = cursor.fetchall()

        questions = []
        for question_row in question_rows:
            question = dict(question_row)

            cursor.execute(
                "SELECT * FROM options_quiz WHERE question_id = ? ORDER BY option_order",
                (question['id'],)
            )
            option_rows = cursor.fetchall()

            options = [dict(option_row) for option_row in option_rows]
            question['options'] = options

            questions.append(question)

        quiz['questions'] = questions
        return quiz
    except Exception as e:
        return None


def delete_quiz(quiz_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM quiz WHERE id = ?", (quiz_id,))
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False


def update_question(question_id, question_text):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "UPDATE questions_quiz SET question_text = ? WHERE id = ?",
            (question_text, question_id)
        )
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False


def delete_question(question_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM questions_quiz WHERE id = ?", (question_id,))
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False


def update_option(option_id, option_text, is_correct):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "UPDATE options_quiz SET option_text = ?, is_correct = ? WHERE id = ?",
            (option_text, is_correct, option_id)
        )
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False


def delete_option(option_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM options_quiz WHERE id = ?", (option_id,))
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False


def add_question(quiz_id, question_text, options=None):
    question_id = add_question_to_quiz(quiz_id, question_text)

    if question_id and options:
        for i, option in enumerate(options):
            add_option_to_question(
                question_id,
                option['text'],
                option['is_correct'],
                i + 1
            )

    return question_id


def update_quiz_details(quiz_id, title, description, class_id, subject_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        # Subject functionality removed - subject_id parameter ignored
        cursor.execute(
            "UPDATE quiz SET title = ?, description = ?, class_id = ?, updated_at = ? WHERE id = ?",
            (title, description, class_id, now, quiz_id)
        )
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False


def create_full_quiz(title, description, class_id, subject_id, questions):
    # Subject functionality removed - subject_id parameter ignored
    quiz_id = create_quiz(title, description, class_id, None)

    if not quiz_id:
        return None

    for i, question_data in enumerate(questions):
        question_id = add_question_to_quiz(
            quiz_id,
            question_data['text'],
            i + 1
        )

        if question_id and 'options' in question_data:
            for j, option_data in enumerate(question_data['options']):
                add_option_to_question(
                    question_id,
                    option_data['text'],
                    option_data['is_correct'],
                    j + 1
                )

    return quiz_id


def submit_quiz(quiz_id, student_name, answers):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        quiz = get_quiz_with_questions(quiz_id)
        if not quiz:
            return None

        score = 0
        total_questions = len(quiz['questions'])
        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO soumissions_quiz (quiz_id, student_name, score, total_questions, submitted_at) VALUES (?, ?, ?, ?, ?)",
            (quiz_id, student_name, 0, total_questions, now)
        )
        submission_id = cursor.lastrowid

        for question in quiz['questions']:
            question_id = question['id']
            selected_option_ids = answers.get(str(question_id), [])

            # Handle both single values and lists for backward compatibility
            if isinstance(selected_option_ids, str):
                selected_option_ids = [selected_option_ids]
            elif not isinstance(selected_option_ids, list):
                selected_option_ids = []

            # Check if ALL selected answers are correct (no wrong answers allowed)
            question_is_correct = False
            if selected_option_ids:
                has_correct_answer = False
                has_wrong_answer = False

                for selected_option_id in selected_option_ids:
                    try:
                        option_id = int(selected_option_id)
                        for option in question['options']:
                            if option['id'] == option_id:
                                if option['is_correct']:
                                    has_correct_answer = True
                                else:
                                    has_wrong_answer = True
                                break
                    except (ValueError, TypeError):
                        continue

                # Only correct if student has at least one correct answer AND no wrong answers
                if has_correct_answer and not has_wrong_answer:
                    question_is_correct = True
                    score += 1

            # Store each selected answer separately
            if selected_option_ids:
                for selected_option_id in selected_option_ids:
                    try:
                        option_id = int(selected_option_id)
                        # Check if this specific option is correct
                        option_is_correct = False
                        for option in question['options']:
                            if option['id'] == option_id and option['is_correct']:
                                option_is_correct = True
                                break

                        cursor.execute(
                            "INSERT INTO reponses_quiz (submission_id, question_id, selected_option_id, is_correct) VALUES (?, ?, ?, ?)",
                            (submission_id, question_id, option_id, option_is_correct)
                        )
                    except (ValueError, TypeError):
                        # Skip invalid option IDs
                        continue
            else:
                # No answer provided for this question
                cursor.execute(
                    "INSERT INTO reponses_quiz (submission_id, question_id, selected_option_id, is_correct) VALUES (?, ?, ?, ?)",
                    (submission_id, question_id, None, False)
                )

        cursor.execute(
            "UPDATE soumissions_quiz SET score = ? WHERE id = ?",
            (score, submission_id)
        )

        conn.commit()
        return {'submission_id': submission_id, 'score': score, 'total': total_questions}
    except Exception as e:
        return None


def get_quiz_submissions(quiz_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "SELECT * FROM soumissions_quiz WHERE quiz_id = ? ORDER BY submitted_at DESC",
            (quiz_id,)
        )
        rows = cursor.fetchall()
        return [dict(row) for row in rows]
    except Exception as e:
        return []


def get_submission_details(submission_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "SELECT * FROM soumissions_quiz WHERE id = ?",
            (submission_id,)
        )
        submission_row = cursor.fetchone()

        if not submission_row:
            return None

        submission = dict(submission_row)

        # Get detailed answers with proper question-level correctness calculation
        cursor.execute("""
            SELECT DISTINCT qq.id as question_id, qq.question_text, qq.question_order
            FROM reponses_quiz qa
            JOIN questions_quiz qq ON qa.question_id = qq.id
            WHERE qa.submission_id = ?
            ORDER BY qq.question_order
        """, (submission_id,))

        question_rows = cursor.fetchall()
        answers = []

        for question_row in question_rows:
            question_id = question_row['question_id']

            # Get all selected answers for this question
            cursor.execute("""
                SELECT qa.selected_option_id, qa.is_correct, qo.option_text
                FROM reponses_quiz qa
                LEFT JOIN options_quiz qo ON qa.selected_option_id = qo.id
                WHERE qa.submission_id = ? AND qa.question_id = ?
            """, (submission_id, question_id))

            selected_answers = cursor.fetchall()

            # Get all correct answers for this question
            cursor.execute("""
                SELECT option_text FROM options_quiz
                WHERE question_id = ? AND is_correct = 1
            """, (question_id,))

            correct_answer_rows = cursor.fetchall()
            if correct_answer_rows:
                correct_answer_texts = [row['option_text'] for row in correct_answer_rows]
                correct_answer_text = ", ".join(correct_answer_texts)
            else:
                correct_answer_text = "No correct answer"

            # Calculate question-level correctness
            # Question is correct if student has at least one correct answer AND no wrong answers
            has_correct_answer = any(answer['is_correct'] for answer in selected_answers if answer['selected_option_id'] is not None)
            has_wrong_answer = any(not answer['is_correct'] for answer in selected_answers if answer['selected_option_id'] is not None)
            question_is_correct = has_correct_answer and not has_wrong_answer

            # Combine selected answer texts
            selected_texts = [answer['option_text'] for answer in selected_answers if answer['option_text'] is not None]
            selected_answer_text = ", ".join(selected_texts) if selected_texts else "No answer"

            answer_data = {
                'question_id': question_id,
                'question_text': question_row['question_text'],
                'question_order': question_row['question_order'],
                'selected_option_text': selected_answer_text,
                'correct_option_text': correct_answer_text,
                'is_correct': question_is_correct
            }
            answers.append(answer_data)

        submission['answers'] = answers
        return submission
    except Exception as e:
        return None


def delete_quiz_submission(submission_id):
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM soumissions_quiz WHERE id = ?", (submission_id,))
        conn.commit()

        return cursor.rowcount > 0
    except Exception as e:
        return False
